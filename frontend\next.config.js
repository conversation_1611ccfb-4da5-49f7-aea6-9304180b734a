/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['images.unsplash.com', 'localhost'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '5001',
        pathname: '/**',
      }
    ],
    // تحسين أداء الصور
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },
  // تحسين الأداء العام
  experimental: {
    optimizeCss: true,
  },
}

module.exports = nextConfig
