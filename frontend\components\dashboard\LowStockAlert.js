// frontend/components/dashboard/LowStockAlert.js
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { getProducts } from '@/lib/api';

export default function LowStockAlert() {
  const [lowStockProducts, setLowStockProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadLowStockProducts();
  }, []);

  const loadLowStockProducts = async () => {
    try {
      const data = await getProducts();
      // التأكد من أن البيانات في الشكل الصحيح
      const products = Array.isArray(data) ? data : data?.products || [];

      // فلترة المنتجات التي مخزونها أقل من الحد الأدنى
      const lowStock = products.filter(product =>
        product.stock <= (product.minStock || 5)
      );
      setLowStockProducts(lowStock.slice(0, 5)); // أول 5 منتجات فقط
    } catch (err) {
      setError('Failed to load low stock products');
      console.error(err);
      // تعيين array فارغ في حالة الخطأ
      setLowStockProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const getStockStatus = (product) => {
    if (product.stock === 0) {
      return { text: 'نفد المخزون', color: 'text-red-600 bg-red-100' };
    } else if (product.stock <= (product.minStock || 5)) {
      return { text: 'مخزون منخفض', color: 'text-yellow-600 bg-yellow-100' };
    }
    return { text: 'متوفر', color: 'text-green-600 bg-green-100' };
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-gray-400 to-gray-500 px-6 py-4">
          <div className="h-6 bg-gray-300 rounded w-1/3"></div>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3 animate-pulse">
                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      <div className="bg-gradient-to-r from-red-500 to-pink-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-white mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h3 className="text-lg font-semibold text-white">تنبيهات المخزون</h3>
          </div>
          {lowStockProducts.length > 0 && (
            <span className="bg-white bg-opacity-20 text-white text-xs px-2 py-1 rounded-full">
              {lowStockProducts.length}
            </span>
          )}
        </div>
      </div>

      <div className="p-6">
        {error ? (
          <div className="text-center py-4">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        ) : lowStockProducts.length === 0 ? (
          <div className="text-center py-8">
            <svg className="mx-auto h-12 w-12 text-green-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-green-600 font-medium">جميع المنتجات متوفرة!</p>
            <p className="text-gray-500 text-sm mt-1">لا توجد منتجات بمخزون منخفض</p>
          </div>
        ) : (
          <div className="space-y-4">
            {lowStockProducts.map((product, index) => {
              const status = getStockStatus(product);
              return (
                <div key={index} className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                  <div className="flex-shrink-0">
                    {(product.image || (product.images && product.images.length > 0)) ? (
                      <Image
                        src={product.image || product.images[0].path}
                        alt={product.name || `منتج ${product.id}`}
                        width={48}
                        height={48}
                        className="w-12 h-12 rounded-lg object-cover border border-gray-200"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                    {/* Fallback for broken images */}
                    <div className="hidden w-12 h-12 bg-gray-200 rounded-lg items-center justify-center">
                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {product.name}
                      </h4>
                      <span className={`text-xs px-2 py-1 rounded-full ${status.color}`}>
                        {status.text}
                      </span>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-sm text-gray-600">
                        المخزون: <span className="font-medium">{product.stock}</span>
                        {product.minStock && (
                          <span className="text-gray-400"> / الحد الأدنى: {product.minStock}</span>
                        )}
                      </p>
                      <Link
                        href={`/dashboard/products/edit/${product.id}`}
                        className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                      >
                        تحديث المخزون
                      </Link>
                    </div>
                  </div>
                </div>
              );
            })}

            {lowStockProducts.length > 0 && (
              <div className="pt-4 border-t border-gray-200">
                <Link
                  href="/dashboard/products?filter=low_stock"
                  className="block w-full text-center py-2 px-4 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors text-sm font-medium"
                >
                  عرض جميع المنتجات منخفضة المخزون
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
