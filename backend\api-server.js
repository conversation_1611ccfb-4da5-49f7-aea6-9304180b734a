// backend/api-server.js
const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 5001;

// إعداد CORS والمعالجة
app.use(cors());
app.use(express.json());

// إنشاء مجلد الصور إذا لم يكن موجوداً
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// إعداد multer لرفع الصور
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  },
  fileFilter: function (req, file, cb) {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('يجب أن يكون الملف صورة'));
    }
  }
});

// تقديم الصور المرفوعة
app.use('/uploads', express.static(uploadsDir));

console.log('🚀 بدء تشغيل خادم API...');

// بيانات تجريبية
let settings = {
  company: {
    companyName: '',
    companyAddress: '',
    companyPhone: '',
    companyEmail: '',
    companyWebsite: '',
    taxId: '',
    currency: 'SAR',
    logoUrl: null
  },
  invoice: {
    invoicePrefix: 'INV',
    invoiceNumberLength: 6,
    defaultTaxRate: 15,
    defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
    autoGenerateInvoiceNumber: true,
    showImagesInInvoice: true,
    allowPartialPayments: true,
    requireCustomerInfo: true,
    defaultDueDays: 30,
    invoiceFooter: '',
    invoiceNotes: '',
    display: {
      showCompanyLogo: true,
      showCompanyName: true,
      showCompanyAddress: true,
      showCompanyPhone: true,
      showCompanyEmail: true,
      showCompanyWebsite: true,
      showTaxId: true,
      showInvoiceNumber: true,
      showInvoiceDate: true,
      showDueDate: true,
      showPaymentTerms: true,
      showCustomerName: true,
      showCustomerAddress: true,
      showCustomerPhone: true,
      showCustomerEmail: true,
      showProductImages: true,
      showProductCode: true,
      showProductDescription: true,
      showQuantity: true,
      showUnitPrice: true,
      showDiscount: false,
      showTotalPrice: true,
      showItemNumbers: true,
      showSubtotal: true,
      showTaxAmount: true,
      showDiscountAmount: false,
      showTotalAmount: true,
      showNotes: true,
      showFooter: true,
      showSignature: false,
      showQRCode: false,
      showBankDetails: false,
      showPaymentInstructions: true
    }
  }
};

// إعداد قاعدة البيانات
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// تم إزالة البيانات الافتراضية - سيتم جلب البيانات من قاعدة البيانات

// مسارات API

// اختبار الخادم
app.get('/api', (req, res) => {
  console.log('✅ GET /api');
  res.json({ message: 'API Server is working!', timestamp: new Date().toISOString() });
});

// إعدادات الفواتير
app.get('/api/settings', (req, res) => {
  console.log('📥 GET /api/settings');
  res.json(settings);
});

app.post('/api/settings/company', (req, res) => {
  console.log('📤 POST /api/settings/company:', req.body);
  settings.company = { ...settings.company, ...req.body };
  res.json({ message: 'تم حفظ إعدادات الشركة بنجاح', settings: settings.company });
});

app.post('/api/settings/invoice', (req, res) => {
  console.log('📤 POST /api/settings/invoice:', req.body);
  settings.invoice = { ...settings.invoice, ...req.body, display: settings.invoice.display };
  res.json({ message: 'تم حفظ إعدادات الفواتير بنجاح', settings: settings.invoice });
});

app.post('/api/settings/invoice/display', (req, res) => {
  console.log('📤 POST /api/settings/invoice/display:', req.body);
  settings.invoice.display = { ...settings.invoice.display, ...req.body };
  res.json({ message: 'تم حفظ إعدادات العرض بنجاح', settings: settings.invoice.display });
});

// مسارات المنتجات
app.get('/api/products', async (req, res) => {
  console.log('📥 GET /api/products');
  try {
    const products = await prisma.product.findMany({
      include: {
        images: {
          orderBy: [
            { isMain: 'desc' },
            { order: 'asc' }
          ]
        }
      }
    });
    res.json({ products, total: products.length });
  } catch (error) {
    console.error('خطأ في جلب المنتجات:', error);
    res.status(500).json({ error: 'فشل في جلب المنتجات', message: error.message });
  }
});

app.get('/api/products/low-stock', async (req, res) => {
  console.log('📥 GET /api/products/low-stock');
  try {
    const lowStockProducts = await prisma.product.findMany({
      where: {
        stock: {
          lt: 20
        }
      },
      include: {
        images: {
          orderBy: [
            { isMain: 'desc' },
            { order: 'asc' }
          ]
        }
      }
    });
    res.json({ products: lowStockProducts, total: lowStockProducts.length });
  } catch (error) {
    console.error('خطأ في جلب المنتجات منخفضة المخزون:', error);
    res.status(500).json({ error: 'فشل في جلب المنتجات منخفضة المخزون', message: error.message });
  }
});

// الحصول على منتج محدد
app.get('/api/products/:id', async (req, res) => {
  console.log(`📥 GET /api/products/${req.params.id}`);
  try {
    const product = await prisma.product.findUnique({
      where: { id: parseInt(req.params.id) },
      include: {
        images: {
          orderBy: [
            { isMain: 'desc' },
            { order: 'asc' }
          ]
        }
      }
    });

    if (product) {
      res.json(product);
    } else {
      res.status(404).json({ error: 'المنتج غير موجود' });
    }
  } catch (error) {
    console.error('خطأ في جلب المنتج:', error);
    res.status(500).json({ error: 'فشل في جلب المنتج', message: error.message });
  }
});

// مسارات الفواتير
app.get('/api/invoices', async (req, res) => {
  console.log('📥 GET /api/invoices');
  try {
    const invoices = await prisma.invoice.findMany({
      include: {
        customer: true,
        items: {
          include: {
            product: {
              include: {
                images: {
                  orderBy: [
                    { isMain: 'desc' },
                    { order: 'asc' }
                  ]
                }
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json({
      invoices,
      total: invoices.length,
      pagination: { page: 1, limit: 10, totalPages: Math.ceil(invoices.length / 10) }
    });
  } catch (error) {
    console.error('خطأ في جلب الفواتير:', error);
    res.status(500).json({ error: 'فشل في جلب الفواتير', message: error.message });
  }
});

app.get('/api/invoices/stats', async (req, res) => {
  console.log('📥 GET /api/invoices/stats');
  try {
    const invoices = await prisma.invoice.findMany();

    const stats = {
      total: invoices.length,
      paid: invoices.filter(i => i.status === 'paid').length,
      pending: invoices.filter(i => i.status === 'pending').length,
      draft: invoices.filter(i => i.status === 'draft').length,
      totalAmount: invoices.reduce((sum, i) => sum + (i.totalAmount || 0), 0),
      paidAmount: invoices.filter(i => i.status === 'paid').reduce((sum, i) => sum + (i.totalAmount || 0), 0)
    };
    res.json(stats);
  } catch (error) {
    console.error('خطأ في جلب إحصائيات الفواتير:', error);
    res.status(500).json({ error: 'فشل في جلب إحصائيات الفواتير', message: error.message });
  }
});

// الحصول على فاتورة محددة
app.get('/api/invoices/:id', async (req, res) => {
  console.log(`📥 GET /api/invoices/${req.params.id}`);
  try {
    const invoice = await prisma.invoice.findUnique({
      where: { id: parseInt(req.params.id) },
      include: {
        customer: true,
        items: {
          include: {
            product: {
              include: {
                images: {
                  orderBy: [
                    { isMain: 'desc' },
                    { order: 'asc' }
                  ]
                }
              }
            }
          }
        }
      }
    });

    if (invoice) {
      res.json(invoice);
    } else {
      res.status(404).json({ error: 'الفاتورة غير موجودة' });
    }
  } catch (error) {
    console.error('خطأ في جلب الفاتورة:', error);
    res.status(500).json({ error: 'فشل في جلب الفاتورة', message: error.message });
  }
});

// الحصول على عميل محدد
app.get('/api/customers/:id', (req, res) => {
  console.log(`📥 GET /api/customers/${req.params.id}`);
  const customers = [
    {
      id: 1,
      name: 'شركة التقنية المتطورة',
      email: '<EMAIL>',
      phone: '+966 11 555 0123',
      address: 'الرياض، المملكة العربية السعودية',
      taxId: '300123456789003',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      name: 'مؤسسة الابتكار التجاري',
      email: '<EMAIL>',
      phone: '+966 12 555 0456',
      address: 'جدة، المملكة العربية السعودية',
      taxId: '300987654321003',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 3,
      name: 'شركة الحلول الذكية',
      email: '<EMAIL>',
      phone: '+966 13 555 0789',
      address: 'الدمام، المملكة العربية السعودية',
      taxId: '300555666777003',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ];
  const customer = customers.find(c => c.id === parseInt(req.params.id));
  if (customer) {
    res.json(customer);
  } else {
    res.status(404).json({ error: 'العميل غير موجود' });
  }
});

// مسارات العملاء
app.get('/api/customers', async (req, res) => {
  console.log('📥 GET /api/customers');
  try {
    const customers = await prisma.customer.findMany({
      orderBy: { createdAt: 'desc' }
    });
    res.json({ customers, total: customers.length });
  } catch (error) {
    console.error('خطأ في جلب العملاء:', error);
    res.status(500).json({ error: 'فشل في جلب العملاء', message: error.message });
  }
});

// إضافة منتج جديد
app.post('/api/products', async (req, res) => {
  console.log('📤 POST /api/products:', req.body);
  try {
    const newProduct = await prisma.product.create({
      data: {
        ...req.body,
        isActive: true
      },
      include: {
        images: {
          orderBy: [
            { isMain: 'desc' },
            { order: 'asc' }
          ]
        }
      }
    });
    res.status(201).json({ message: 'تم إضافة المنتج بنجاح', product: newProduct });
  } catch (error) {
    console.error('خطأ في إضافة المنتج:', error);
    res.status(500).json({ error: 'فشل في إضافة المنتج', message: error.message });
  }
});

// تحديث منتج
app.put('/api/products/:id', async (req, res) => {
  console.log(`📤 PUT /api/products/${req.params.id}:`, req.body);
  try {
    const updatedProduct = await prisma.product.update({
      where: { id: parseInt(req.params.id) },
      data: req.body,
      include: {
        images: {
          orderBy: [
            { isMain: 'desc' },
            { order: 'asc' }
          ]
        }
      }
    });
    res.json({ message: 'تم تحديث المنتج بنجاح', product: updatedProduct });
  } catch (error) {
    console.error('خطأ في تحديث المنتج:', error);
    if (error.code === 'P2025') {
      res.status(404).json({ error: 'المنتج غير موجود' });
    } else {
      res.status(500).json({ error: 'فشل في تحديث المنتج', message: error.message });
    }
  }
});

// حذف منتج
app.delete('/api/products/:id', async (req, res) => {
  console.log(`🗑️ DELETE /api/products/${req.params.id}`);
  try {
    await prisma.product.delete({
      where: { id: parseInt(req.params.id) }
    });
    res.json({ message: 'تم حذف المنتج بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المنتج:', error);
    if (error.code === 'P2025') {
      res.status(404).json({ error: 'المنتج غير موجود' });
    } else {
      res.status(500).json({ error: 'فشل في حذف المنتج', message: error.message });
    }
  }
});

// إضافة عميل جديد
app.post('/api/customers', async (req, res) => {
  console.log('📤 POST /api/customers:', req.body);
  try {
    const newCustomer = await prisma.customer.create({
      data: {
        ...req.body,
        isActive: true
      }
    });
    res.status(201).json({ message: 'تم إضافة العميل بنجاح', customer: newCustomer });
  } catch (error) {
    console.error('خطأ في إضافة العميل:', error);
    res.status(500).json({ error: 'فشل في إضافة العميل', message: error.message });
  }
});

// تحديث عميل
app.put('/api/customers/:id', async (req, res) => {
  console.log(`📤 PUT /api/customers/${req.params.id}:`, req.body);
  try {
    const updatedCustomer = await prisma.customer.update({
      where: { id: parseInt(req.params.id) },
      data: req.body
    });
    res.json({ message: 'تم تحديث العميل بنجاح', customer: updatedCustomer });
  } catch (error) {
    console.error('خطأ في تحديث العميل:', error);
    if (error.code === 'P2025') {
      res.status(404).json({ error: 'العميل غير موجود' });
    } else {
      res.status(500).json({ error: 'فشل في تحديث العميل', message: error.message });
    }
  }
});

// حذف عميل
app.delete('/api/customers/:id', async (req, res) => {
  console.log(`🗑️ DELETE /api/customers/${req.params.id}`);
  try {
    await prisma.customer.delete({
      where: { id: parseInt(req.params.id) }
    });
    res.json({ message: 'تم حذف العميل بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف العميل:', error);
    if (error.code === 'P2025') {
      res.status(404).json({ error: 'العميل غير موجود' });
    } else {
      res.status(500).json({ error: 'فشل في حذف العميل', message: error.message });
    }
  }
});

// إضافة فاتورة جديدة
app.post('/api/invoices', async (req, res) => {
  console.log('📤 POST /api/invoices:', req.body);
  try {
    // إنشاء رقم فاتورة تلقائي
    const invoiceCount = await prisma.invoice.count();
    const invoiceNumber = `INV-${String(invoiceCount + 1).padStart(3, '0')}`;

    const newInvoice = await prisma.invoice.create({
      data: {
        ...req.body,
        invoiceNumber,
        status: req.body.status || 'draft',
        paymentStatus: req.body.paymentStatus || 'unpaid'
      },
      include: {
        customer: true,
        items: {
          include: {
            product: {
              include: {
                images: {
                  orderBy: [
                    { isMain: 'desc' },
                    { order: 'asc' }
                  ]
                }
              }
            }
          }
        }
      }
    });
    res.status(201).json({ message: 'تم إنشاء الفاتورة بنجاح', invoice: newInvoice });
  } catch (error) {
    console.error('خطأ في إنشاء الفاتورة:', error);
    res.status(500).json({ error: 'فشل في إنشاء الفاتورة', message: error.message });
  }
});

// تحديث فاتورة
app.put('/api/invoices/:id', async (req, res) => {
  console.log(`📤 PUT /api/invoices/${req.params.id}:`, req.body);
  try {
    const updatedInvoice = await prisma.invoice.update({
      where: { id: parseInt(req.params.id) },
      data: req.body,
      include: {
        customer: true,
        items: {
          include: {
            product: {
              include: {
                images: {
                  orderBy: [
                    { isMain: 'desc' },
                    { order: 'asc' }
                  ]
                }
              }
            }
          }
        }
      }
    });
    res.json({ message: 'تم تحديث الفاتورة بنجاح', invoice: updatedInvoice });
  } catch (error) {
    console.error('خطأ في تحديث الفاتورة:', error);
    if (error.code === 'P2025') {
      res.status(404).json({ error: 'الفاتورة غير موجودة' });
    } else {
      res.status(500).json({ error: 'فشل في تحديث الفاتورة', message: error.message });
    }
  }
});

// حذف فاتورة
app.delete('/api/invoices/:id', async (req, res) => {
  console.log(`🗑️ DELETE /api/invoices/${req.params.id}`);
  try {
    await prisma.invoice.delete({
      where: { id: parseInt(req.params.id) }
    });
    res.json({ message: 'تم حذف الفاتورة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الفاتورة:', error);
    if (error.code === 'P2025') {
      res.status(404).json({ error: 'الفاتورة غير موجودة' });
    } else {
      res.status(500).json({ error: 'فشل في حذف الفاتورة', message: error.message });
    }
  }
});

// مسارات رفع الصور
app.post('/api/products/:id/images', upload.single('image'), async (req, res) => {
  console.log(`📤 POST /api/products/${req.params.id}/images`);

  if (!req.file) {
    return res.status(400).json({ error: 'لم يتم رفع أي صورة' });
  }

  try {
    const productId = parseInt(req.params.id);

    // التحقق من وجود المنتج
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      // حذف الملف المرفوع إذا لم يكن المنتج موجوداً
      fs.unlinkSync(req.file.path);
      return res.status(404).json({ error: 'المنتج غير موجود' });
    }

    // حفظ معلومات الصورة في قاعدة البيانات
    const image = await prisma.productImage.create({
      data: {
        filename: req.file.filename,
        originalName: req.file.originalname,
        path: `uploads/${req.file.filename}`,
        size: req.file.size,
        mimeType: req.file.mimetype,
        productId: productId,
        isMain: false, // سيتم تحديدها لاحقاً
        order: 0
      }
    });

    // إذا كانت هذه أول صورة للمنتج، اجعلها رئيسية
    const imageCount = await prisma.productImage.count({
      where: { productId: productId }
    });

    if (imageCount === 1) {
      await prisma.productImage.update({
        where: { id: image.id },
        data: { isMain: true }
      });
      image.isMain = true;
    }

    res.status(201).json({
      message: 'تم رفع الصورة بنجاح',
      image: image
    });

  } catch (error) {
    console.error('خطأ في رفع الصورة:', error);

    // حذف الملف المرفوع في حالة الخطأ
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      error: 'فشل في رفع الصورة',
      message: error.message
    });
  }
});

// الحصول على صور المنتج
app.get('/api/products/:id/images', async (req, res) => {
  console.log(`📥 GET /api/products/${req.params.id}/images`);

  try {
    const productId = parseInt(req.params.id);

    const images = await prisma.productImage.findMany({
      where: { productId: productId },
      orderBy: [
        { isMain: 'desc' },
        { order: 'asc' },
        { createdAt: 'asc' }
      ]
    });

    res.json(images);
  } catch (error) {
    console.error('خطأ في جلب الصور:', error);
    res.status(500).json({
      error: 'فشل في جلب الصور',
      message: error.message
    });
  }
});

// حذف صورة
app.delete('/api/images/:imageId', async (req, res) => {
  console.log(`🗑️ DELETE /api/images/${req.params.imageId}`);

  try {
    const imageId = parseInt(req.params.imageId);

    // الحصول على معلومات الصورة
    const image = await prisma.productImage.findUnique({
      where: { id: imageId }
    });

    if (!image) {
      return res.status(404).json({ error: 'الصورة غير موجودة' });
    }

    // حذف الملف من النظام
    const filePath = path.join(__dirname, image.path);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // حذف السجل من قاعدة البيانات
    await prisma.productImage.delete({
      where: { id: imageId }
    });

    // إذا كانت الصورة المحذوفة رئيسية، اجعل أول صورة أخرى رئيسية
    if (image.isMain) {
      const firstImage = await prisma.productImage.findFirst({
        where: { productId: image.productId },
        orderBy: { createdAt: 'asc' }
      });

      if (firstImage) {
        await prisma.productImage.update({
          where: { id: firstImage.id },
          data: { isMain: true }
        });
      }
    }

    res.json({ message: 'تم حذف الصورة بنجاح' });

  } catch (error) {
    console.error('خطأ في حذف الصورة:', error);
    res.status(500).json({
      error: 'فشل في حذف الصورة',
      message: error.message
    });
  }
});

// تحديد الصورة الرئيسية
app.put('/api/images/:imageId/main', async (req, res) => {
  console.log(`📤 PUT /api/images/${req.params.imageId}/main`);

  try {
    const imageId = parseInt(req.params.imageId);

    // الحصول على معلومات الصورة
    const image = await prisma.productImage.findUnique({
      where: { id: imageId }
    });

    if (!image) {
      return res.status(404).json({ error: 'الصورة غير موجودة' });
    }

    // إزالة الصورة الرئيسية الحالية
    await prisma.productImage.updateMany({
      where: {
        productId: image.productId,
        isMain: true
      },
      data: { isMain: false }
    });

    // تحديد الصورة الجديدة كرئيسية
    await prisma.productImage.update({
      where: { id: imageId },
      data: { isMain: true }
    });

    res.json({ message: 'تم تحديد الصورة الرئيسية بنجاح' });

  } catch (error) {
    console.error('خطأ في تحديد الصورة الرئيسية:', error);
    res.status(500).json({
      error: 'فشل في تحديد الصورة الرئيسية',
      message: error.message
    });
  }
});

// مسارات المصادقة (تجريبية)
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 POST /api/auth/login:', req.body);
  // مصادقة تجريبية - قبول أي بيانات
  const { email, password } = req.body;
  if (email && password) {
    res.json({
      user: { id: 1, email, name: 'مستخدم تجريبي' },
      token: 'fake-jwt-token-for-testing'
    });
  } else {
    res.status(400).json({ error: 'البريد الإلكتروني وكلمة المرور مطلوبان' });
  }
});

app.post('/api/auth/register', (req, res) => {
  console.log('📝 POST /api/auth/register:', req.body);
  // تسجيل تجريبي - قبول أي بيانات
  const { name, email, password } = req.body;
  if (name && email && password) {
    res.json({
      user: { id: 1, email, name },
      token: 'fake-jwt-token-for-testing'
    });
  } else {
    res.status(400).json({ error: 'جميع الحقول مطلوبة' });
  }
});

// معالج الأخطاء
app.use((err, req, res, next) => {
  console.error('❌ خطأ في الخادم:', err);
  res.status(500).json({ error: 'حدث خطأ في الخادم', message: err.message });
});

// معالج المسارات غير الموجودة
app.use((req, res) => {
  console.log(`❓ مسار غير موجود: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ error: 'المسار غير موجود', path: req.originalUrl });
});

// تشغيل الخادم
app.listen(PORT, () => {
  console.log(`🚀 خادم API يعمل على المنفذ ${PORT}`);
  console.log(`📡 API متاح على: http://localhost:${PORT}/api`);
  console.log(`⚙️ الإعدادات: http://localhost:${PORT}/api/settings`);
  console.log(`📦 المنتجات: http://localhost:${PORT}/api/products`);
  console.log(`📄 الفواتير: http://localhost:${PORT}/api/invoices`);
  console.log('✅ جاهز لاستقبال الطلبات');
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 إنهاء الخادم...');
  process.exit(0);
});
