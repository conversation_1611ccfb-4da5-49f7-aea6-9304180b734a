// backend/test-image-server.js
const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 5001;

// إعداد CORS والمعالجة
app.use(cors());
app.use(express.json());

// إنشاء مجلد الصور إذا لم يكن موجوداً
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// إعداد multer لرفع الصور
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  },
  fileFilter: function (req, file, cb) {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('يجب أن يكون الملف صورة'));
    }
  }
});

// تقديم الصور المرفوعة
app.use('/uploads', express.static(uploadsDir));

// اختبار الخادم
app.get('/api', (req, res) => {
  console.log('✅ GET /api');
  res.json({ message: 'Test Image Server is working!', timestamp: new Date().toISOString() });
});

// مسار رفع الصور (بدون مصادقة للاختبار)
app.post('/api/products/:id/images', upload.single('image'), async (req, res) => {
  console.log(`📤 POST /api/products/${req.params.id}/images`);
  console.log('File received:', req.file);

  if (!req.file) {
    return res.status(400).json({ error: 'لم يتم رفع أي صورة' });
  }

  try {
    const productId = parseInt(req.params.id);
    console.log('Product ID:', productId);

    // محاكاة حفظ معلومات الصورة (بدون قاعدة بيانات)
    const image = {
      id: Date.now(),
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: `uploads/${req.file.filename}`,
      size: req.file.size,
      mimeType: req.file.mimetype,
      productId: productId,
      isMain: true,
      order: 0,
      createdAt: new Date().toISOString()
    };

    console.log('Image saved:', image);

    res.status(201).json({
      message: 'تم رفع الصورة بنجاح',
      image: image
    });

  } catch (error) {
    console.error('خطأ في رفع الصورة:', error);

    // حذف الملف المرفوع في حالة الخطأ
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      error: 'فشل في رفع الصورة',
      message: error.message
    });
  }
});

// مسار جلب صور المنتج (محاكاة)
app.get('/api/products/:id/images', (req, res) => {
  console.log(`📥 GET /api/products/${req.params.id}/images`);
  
  // محاكاة إرجاع قائمة فارغة من الصور
  res.json([]);
});

// بدء تشغيل الخادم
app.listen(PORT, () => {
  console.log('🚀 بدء تشغيل خادم اختبار الصور...');
  console.log(`🚀 خادم اختبار الصور يعمل على المنفذ ${PORT}`);
  console.log(`📡 API متاح على: http://localhost:${PORT}/api`);
  console.log(`📦 رفع الصور: http://localhost:${PORT}/api/products/:id/images`);
  console.log('✅ جاهز لاستقبال الطلبات');
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 إنهاء الخادم...');
  process.exit(0);
});
