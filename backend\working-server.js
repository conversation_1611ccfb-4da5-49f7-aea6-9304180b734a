// backend/working-server.js
const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 5001;

// إعداد middleware
app.use(cors({
  origin: ['http://localhost:3002', 'http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// إنشاء مجلد الصور إذا لم يكن موجوداً
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// إعداد multer لرفع الصور
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  },
  fileFilter: function (req, file, cb) {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('يجب أن يكون الملف صورة'));
    }
  }
});

// تقديم الصور المرفوعة
app.use('/uploads', express.static(uploadsDir));

// بيانات تجريبية للإعدادات (في الذاكرة)
let invoiceSettings = {
  company: {
    companyName: '',
    companyAddress: '',
    companyPhone: '',
    companyEmail: '',
    companyWebsite: '',
    taxId: '',
    currency: 'SAR',
    logoUrl: null
  },
  invoice: {
    invoicePrefix: 'INV',
    invoiceNumberLength: 6,
    defaultTaxRate: 15,
    defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
    autoGenerateInvoiceNumber: true,
    showImagesInInvoice: true,
    allowPartialPayments: true,
    requireCustomerInfo: true,
    defaultDueDays: 30,
    invoiceFooter: '',
    invoiceNotes: '',
    display: {
      // رأس الفاتورة
      showCompanyLogo: true,
      showCompanyName: true,
      showCompanyAddress: true,
      showCompanyPhone: true,
      showCompanyEmail: true,
      showCompanyWebsite: true,
      showTaxId: true,

      // معلومات الفاتورة
      showInvoiceNumber: true,
      showInvoiceDate: true,
      showDueDate: true,
      showPaymentTerms: true,

      // معلومات العميل
      showCustomerName: true,
      showCustomerAddress: true,
      showCustomerPhone: true,
      showCustomerEmail: true,

      // عناصر الجدول
      showProductImages: true,
      showProductCode: true,
      showProductDescription: true,
      showQuantity: true,
      showUnitPrice: true,
      showDiscount: false,
      showTotalPrice: true,
      showItemNumbers: true,

      // المجاميع
      showSubtotal: true,
      showTaxAmount: true,
      showDiscountAmount: false,
      showTotalAmount: true,

      // التذييل
      showNotes: true,
      showFooter: true,
      showSignature: false,
      showQRCode: false,
      showBankDetails: false,
      showPaymentInstructions: true
    }
  }
};

// Middleware لمعالجة الأخطاء
const errorHandler = (err, req, res, next) => {
  console.error('❌ خطأ في الخادم:', err);
  res.status(500).json({
    error: 'حدث خطأ في الخادم',
    message: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
};

// مسار اختبار
app.get('/api', (req, res) => {
  try {
    console.log('✅ GET /api - اختبار الخادم');
    res.json({
      message: 'Invoice Settings API is working!',
      timestamp: new Date().toISOString(),
      status: 'OK'
    });
  } catch (error) {
    console.error('❌ خطأ في /api:', error);
    res.status(500).json({ error: 'خطأ في اختبار الخادم' });
  }
});

// الحصول على جميع الإعدادات
app.get('/api/settings', (req, res) => {
  try {
    console.log('📥 GET /api/settings - تحميل الإعدادات');
    res.json(invoiceSettings);
  } catch (error) {
    console.error('❌ خطأ في تحميل الإعدادات:', error);
    res.status(500).json({ error: 'فشل في تحميل الإعدادات', details: error.message });
  }
});

// حفظ إعدادات الشركة
app.post('/api/settings/company', (req, res) => {
  try {
    console.log('📤 POST /api/settings/company - حفظ إعدادات الشركة');
    console.log('البيانات المستلمة:', req.body);

    // تحديث إعدادات الشركة
    invoiceSettings.company = {
      ...invoiceSettings.company,
      ...req.body
    };

    console.log('✅ تم تحديث إعدادات الشركة:', invoiceSettings.company);

    res.json({
      message: 'تم حفظ إعدادات الشركة بنجاح',
      settings: invoiceSettings.company,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ خطأ في حفظ إعدادات الشركة:', error);
    res.status(500).json({ error: 'فشل في حفظ إعدادات الشركة', details: error.message });
  }
});

// حفظ إعدادات الفواتير العامة
app.post('/api/settings/invoice', (req, res) => {
  try {
    console.log('📤 POST /api/settings/invoice - حفظ إعدادات الفواتير');
    console.log('البيانات المستلمة:', req.body);

    // تحديث إعدادات الفواتير (مع الحفاظ على إعدادات العرض)
    const currentDisplay = invoiceSettings.invoice.display;
    invoiceSettings.invoice = {
      ...invoiceSettings.invoice,
      ...req.body,
      display: currentDisplay
    };

    console.log('✅ تم تحديث إعدادات الفواتير:', invoiceSettings.invoice);

    res.json({
      message: 'تم حفظ إعدادات الفواتير بنجاح',
      settings: invoiceSettings.invoice,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ خطأ في حفظ إعدادات الفواتير:', error);
    res.status(500).json({ error: 'فشل في حفظ إعدادات الفواتير', details: error.message });
  }
});

// حفظ إعدادات عرض الفاتورة
app.post('/api/settings/invoice/display', (req, res) => {
  try {
    console.log('📤 POST /api/settings/invoice/display - حفظ إعدادات العرض');
    console.log('البيانات المستلمة:', req.body);

    // تحديث إعدادات العرض
    invoiceSettings.invoice.display = {
      ...invoiceSettings.invoice.display,
      ...req.body
    };

    console.log('✅ تم تحديث إعدادات العرض:', invoiceSettings.invoice.display);

    res.json({
      message: 'تم حفظ إعدادات عرض الفاتورة بنجاح',
      settings: invoiceSettings.invoice.display,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ خطأ في حفظ إعدادات العرض:', error);
    res.status(500).json({ error: 'فشل في حفظ إعدادات العرض', details: error.message });
  }
});

// مسار للحصول على إعدادات الشركة فقط
app.get('/api/settings/company', (req, res) => {
  try {
    console.log('📥 GET /api/settings/company - تحميل إعدادات الشركة');
    res.json(invoiceSettings.company);
  } catch (error) {
    console.error('❌ خطأ في تحميل إعدادات الشركة:', error);
    res.status(500).json({ error: 'فشل في تحميل إعدادات الشركة', details: error.message });
  }
});

// مسار للحصول على إعدادات العرض فقط
app.get('/api/settings/invoice/display', (req, res) => {
  try {
    console.log('📥 GET /api/settings/invoice/display - تحميل إعدادات العرض');
    res.json(invoiceSettings.invoice.display);
  } catch (error) {
    console.error('❌ خطأ في تحميل إعدادات العرض:', error);
    res.status(500).json({ error: 'فشل في تحميل إعدادات العرض', details: error.message });
  }
});

// مسار لإعادة تعيين الإعدادات
app.post('/api/settings/reset', (req, res) => {
  try {
    console.log('🔄 POST /api/settings/reset - إعادة تعيين الإعدادات');

    // إعادة تعيين الإعدادات للقيم الافتراضية
    invoiceSettings = {
      company: {
        companyName: '',
        companyAddress: '',
        companyPhone: '',
        companyEmail: '',
        companyWebsite: '',
        taxId: '',
        currency: 'SAR',
        logoUrl: null
      },
      invoice: {
        invoicePrefix: 'INV',
        invoiceNumberLength: 6,
        defaultTaxRate: 15,
        defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
        autoGenerateInvoiceNumber: true,
        showImagesInInvoice: true,
        allowPartialPayments: true,
        requireCustomerInfo: true,
        defaultDueDays: 30,
        invoiceFooter: '',
        invoiceNotes: '',
        display: {
          showCompanyLogo: true,
          showCompanyName: true,
          showCompanyAddress: true,
          showCompanyPhone: true,
          showCompanyEmail: true,
          showCompanyWebsite: true,
          showTaxId: true,
          showInvoiceNumber: true,
          showInvoiceDate: true,
          showDueDate: true,
          showPaymentTerms: true,
          showCustomerName: true,
          showCustomerAddress: true,
          showCustomerPhone: true,
          showCustomerEmail: true,
          showProductImages: true,
          showProductCode: true,
          showProductDescription: true,
          showQuantity: true,
          showUnitPrice: true,
          showDiscount: false,
          showTotalPrice: true,
          showItemNumbers: true,
          showSubtotal: true,
          showTaxAmount: true,
          showDiscountAmount: false,
          showTotalAmount: true,
          showNotes: true,
          showFooter: true,
          showSignature: false,
          showQRCode: false,
          showBankDetails: false,
          showPaymentInstructions: true
        }
      }
    };

    console.log('✅ تم إعادة تعيين الإعدادات');

    res.json({
      message: 'تم إعادة تعيين الإعدادات بنجاح',
      settings: invoiceSettings,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين الإعدادات:', error);
    res.status(500).json({ error: 'فشل في إعادة تعيين الإعدادات', details: error.message });
  }
});

// معالج الأخطاء
app.use(errorHandler);

// معالج للمسارات غير الموجودة
// مسارات رفع الصور
app.post('/api/products/:id/images', upload.single('image'), async (req, res) => {
  console.log(`📤 POST /api/products/${req.params.id}/images`);
  console.log('File received:', req.file);

  if (!req.file) {
    return res.status(400).json({ error: 'لم يتم رفع أي صورة' });
  }

  try {
    const productId = parseInt(req.params.id);
    console.log('Product ID:', productId);

    // محاكاة حفظ معلومات الصورة (بدون قاعدة بيانات)
    const image = {
      id: Date.now(),
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: `uploads/${req.file.filename}`,
      size: req.file.size,
      mimeType: req.file.mimetype,
      productId: productId,
      isMain: true,
      order: 0,
      createdAt: new Date().toISOString()
    };

    console.log('Image saved:', image);

    res.status(201).json({
      message: 'تم رفع الصورة بنجاح',
      image: image
    });

  } catch (error) {
    console.error('خطأ في رفع الصورة:', error);

    // حذف الملف المرفوع في حالة الخطأ
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      error: 'فشل في رفع الصورة',
      message: error.message
    });
  }
});

// مسار جلب صور المنتج (محاكاة)
app.get('/api/products/:id/images', (req, res) => {
  console.log(`📥 GET /api/products/${req.params.id}/images`);

  // محاكاة إرجاع قائمة فارغة من الصور
  res.json([]);
});

app.use((req, res) => {
  console.log(`❓ مسار غير موجود: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    error: 'المسار غير موجود',
    path: req.originalUrl,
    method: req.method
  });
});

// تشغيل الخادم
app.listen(PORT, () => {
  console.log('🚀 خادم إعدادات الفواتير يعمل بنجاح!');
  console.log(`📡 API متاح على: http://localhost:${PORT}/api`);
  console.log(`⚙️ إعدادات الفواتير: http://localhost:${PORT}/api/settings`);
  console.log(`🏢 إعدادات الشركة: http://localhost:${PORT}/api/settings/company`);
  console.log(`👁️ إعدادات العرض: http://localhost:${PORT}/api/settings/invoice/display`);
  console.log('📝 جاهز لاستقبال الطلبات...');
});
